'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';

interface EmployeeFromAPI {
  employee_id: string;
  first_name: string;
  last_name: string;
  full_name: string;
  email: string | null;
  phone_number: string | null;
  position: string | null;
  status: string;
  hire_date: string | null;
  id_number?: string | null;
  department_id: string | null;
  created_at?: string;
  updated_at?: string;
}

const EmployeeDebug: React.FC = () => {
  const { companies } = useAuth();
  const [employees, setEmployees] = useState<EmployeeFromAPI[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [rawResponse, setRawResponse] = useState<any>(null);

  const fetchEmployees = async () => {
    try {
      if (!companies || companies.length === 0) {
        setError('No companies available');
        return;
      }

      setIsLoading(true);
      setError('');

      const companyId = companies[0].company_id;
      const { apiGet } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        setError('No access token available');
        return;
      }

      console.log(`Fetching employees for company: ${companyId}`);

      const response = await apiGet<any>(
        `api/employees?company_id=${companyId}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );

      console.log('Raw API response:', response);
      setRawResponse(response);

      if (response.extend && response.extend.employees && Array.isArray(response.extend.employees)) {
        const employeesList = response.extend.employees;
        console.log('Found employees:', employeesList.length);
        setEmployees(employeesList);
      } else {
        // Don't set error for empty data - this is normal for new companies
        console.log('No employees found in response structure - this is normal for new companies');
        setEmployees([]);
      }
    } catch (error: any) {
      console.error('Error fetching employees:', error);
      setError(`Failed to fetch employees: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (companies && companies.length > 0) {
      fetchEmployees();
    }
  }, [companies]);

  return (
    <div className="p-6 bg-white rounded-lg shadow-sm">
      <h2 className="text-xl font-bold mb-4">Employee Debug Information</h2>
      
      <div className="space-y-4">
        <div>
          <h3 className="font-medium">Company Info:</h3>
          <pre className="bg-gray-100 p-2 rounded text-sm">
            {JSON.stringify(companies, null, 2)}
          </pre>
        </div>

        <div>
          <button
            onClick={fetchEmployees}
            disabled={isLoading}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
          >
            {isLoading ? 'Loading...' : 'Fetch Employees'}
          </button>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded">
            {error}
          </div>
        )}

        {rawResponse && (
          <div>
            <h3 className="font-medium">Raw API Response:</h3>
            <pre className="bg-gray-100 p-2 rounded text-sm max-h-40 overflow-auto">
              {JSON.stringify(rawResponse, null, 2)}
            </pre>
          </div>
        )}

        <div>
          <h3 className="font-medium">Employees Found: {employees.length}</h3>
          {employees.length > 0 && (
            <div className="space-y-2 mt-2">
              {employees.map((employee) => (
                <div key={employee.employee_id} className="bg-gray-50 p-3 rounded">
                  <div className="font-medium">{employee.full_name}</div>
                  <div className="text-sm text-gray-600">
                    ID: {employee.employee_id} | Position: {employee.position || 'N/A'} | Status: {employee.status}
                  </div>
                  <div className="text-xs text-gray-500">
                    Email: {employee.email || 'N/A'} | Department ID: {employee.department_id || 'N/A'}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EmployeeDebug;
