'use client';

import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';

interface CompanyRegistrationFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

const CompanyRegistrationForm: React.FC<CompanyRegistrationFormProps> = ({
  onSuccess,
  onCancel
}) => {
  const { registerCompany, refreshUserData } = useAuth();
  const [formData, setFormData] = useState({
    company_name: '',
    company_tin: '',
    phone_number: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    setSuccessMessage('');

    // Basic validation
    if (!formData.company_name.trim()) {
      setError('Company name is required');
      setIsLoading(false);
      return;
    }

    if (!formData.company_tin.trim()) {
      setError('TIN number is required');
      setIsLoading(false);
      return;
    }

    try {
      // Prepare the data
      const companyData: {
        company_name: string;
        company_tin: string;
        phone_number?: string;
      } = {
        company_name: formData.company_name.trim(),
        company_tin: formData.company_tin.trim()
      };

      // Only add phone_number if it's not empty
      if (formData.phone_number.trim()) {
        companyData.phone_number = formData.phone_number.trim();
      }

      // Log the data being sent
      console.log('Submitting company data:', companyData);
      console.log('Data types:', {
        company_name_type: typeof companyData.company_name,
        company_tin_type: typeof companyData.company_tin,
        phone_number_type: companyData.phone_number ? typeof companyData.phone_number : 'undefined'
      });
      console.log('JSON string:', JSON.stringify(companyData));

      // Register the company
      const response = await registerCompany(companyData);
      console.log('Company registration response:', response);

      // Handle success
      setSuccessMessage(
        response.message ||
        'Company registered successfully! Your dashboard will be updated.'
      );

      // Refresh user data to get the latest company information
      await refreshUserData();

      // Call the onSuccess callback if provided
      if (onSuccess) {
        setTimeout(() => {
          onSuccess();
        }, 1500);
      }
    } catch (error: any) {
      console.error('Error registering company:', error);

      // Use the error message from the API (which now includes proper error extraction)
      setError(error.message || 'Failed to register company. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-xl font-semibold text-secondary-dark mb-4">Register Your Company</h2>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm mb-4">
          {error}
        </div>
      )}

      {successMessage && (
        <div className="bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-md text-sm mb-4">
          {successMessage}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="company_name" className="block text-sm font-medium text-secondary-dark mb-1">
            Company Name *
          </label>
          <input
            id="company_name"
            name="company_name"
            type="text"
            required
            value={formData.company_name}
            onChange={handleChange}
            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            placeholder="Enter your company name"
          />
        </div>

        <div>
          <label htmlFor="company_tin" className="block text-sm font-medium text-secondary-dark mb-1">
            TIN Number *
          </label>
          <input
            id="company_tin"
            name="company_tin"
            type="text"
            required
            value={formData.company_tin}
            onChange={handleChange}
            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            placeholder="Enter your company TIN number"
          />
        </div>

        <div>
          <label htmlFor="phone_number" className="block text-sm font-medium text-secondary-dark mb-1">
            Phone Number (Optional)
          </label>
          <input
            id="phone_number"
            name="phone_number"
            type="tel"
            value={formData.phone_number}
            onChange={handleChange}
            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            placeholder="Enter company phone number"
          />
        </div>

        <div className="flex justify-end space-x-3 pt-4">
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              className="btn-outline py-2 px-4"
              disabled={isLoading}
            >
              Cancel
            </button>
          )}

          <button
            type="submit"
            disabled={isLoading}
            className="btn-primary py-2 px-6 flex items-center justify-center"
          >
            {isLoading ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Registering...
              </>
            ) : (
              'Register Company'
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default CompanyRegistrationForm;
