'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import DashboardCard from '@/components/ui/DashboardCard';
import { createSalaryFromScenario, SalaryScenarioData } from '@/lib/payrollApi';


interface Employee {
  employee_id: string;
  first_name: string;
  last_name: string;
  full_name: string;
  email: string | null;
  phone_number: string | null;
  position: string | null;
  status: string;
  hire_date: string | null;
  department_id: string | null;
  created_at?: string;
  updated_at?: string;
  id_number?: string | null;
}

interface EmployeeResponse {
  code: number;
  extend: {
    employees: Employee[];
    pagination: {
      has_next: boolean;
      has_prev: boolean;
      page: number;
      pages: number;
      per_page: number;
      total_count: number;
    };
  };
  msg: string;
}



const PayrollContent = () => {
  const { companies } = useAuth();
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null);
  const [isLoadingEmployees, setIsLoadingEmployees] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Form state
  const [formData, setFormData] = useState<{
    scenario: 'net_plus_allowances' | 'gross_minus_deductions' | 'basic_plus_allowances';
    net_salary: string;
    gross_salary: string;
    basic_salary: string;
    transport: string;
    housing: string;
    communication: string;
    medical: string;
    overtime: string;
    bonus: string;
    effective_from: string;
  }>({
    scenario: 'net_plus_allowances',
    net_salary: '',
    gross_salary: '',
    basic_salary: '',
    transport: '',
    housing: '',
    communication: '',
    medical: '',
    overtime: '',
    bonus: '',
    effective_from: new Date().toISOString().split('T')[0],
  });

  useEffect(() => {
    fetchEmployees();
  }, [companies]);

  // Function to fetch employees from API
  const fetchEmployees = async () => {
    try {
      setIsLoadingEmployees(true);
      setError('');

      const { apiGet } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        setError('Authentication required');
        setEmployees([]);
        setIsLoadingEmployees(false);
        return;
      }

      const companyId = companies && companies.length > 0 ? companies[0].company_id : null;

      if (!companyId) {
        // Don't show error for missing company - show helpful message instead
        setEmployees([]);
        setIsLoadingEmployees(false);
        return;
      }

      const response = await apiGet<EmployeeResponse>(
        `api/employees?company_id=${companyId}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );

      if (response.extend && response.extend.employees) {
        setEmployees(response.extend.employees);
      } else {
        setEmployees([]);
      }
    } catch (error) {
      console.error('Error fetching employees:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch employees');
      setEmployees([]);
    } finally {
      setIsLoadingEmployees(false);
    }
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear error when user starts typing
    if (error) setError('');
  };

  // Handle employee selection
  const handleEmployeeSelect = (employee: Employee) => {
    setSelectedEmployee(employee);
    setError('');
  };

  // Validate form
  const validateForm = () => {
    if (!selectedEmployee) {
      setError('Please select an employee');
      return false;
    }

    if (!formData.effective_from) {
      setError('Effective date is required');
      return false;
    }

    if (formData.scenario === 'net_plus_allowances' && !formData.net_salary) {
      setError('Net salary is required for this scenario');
      return false;
    }

    if (formData.scenario === 'gross_minus_deductions' && !formData.gross_salary) {
      setError('Gross salary is required for this scenario');
      return false;
    }

    if (formData.scenario === 'basic_plus_allowances' && !formData.basic_salary) {
      setError('Basic salary is required for this scenario');
      return false;
    }

    return true;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setError('');
    setSuccess('');

    try {
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      const companyId = companies && companies.length > 0 ? companies[0].company_id : null;

      if (!companyId) {
        throw new Error('Please register a company first to calculate payroll.');
      }

      // Prepare the salary data
      const salaryData: SalaryScenarioData = {
        scenario: formData.scenario,
        known_values: {},
        effective_from: formData.effective_from,
      };

      // Add values based on scenario and form data
      if (formData.net_salary) salaryData.known_values.net_salary = parseFloat(formData.net_salary);
      if (formData.gross_salary) salaryData.known_values.gross_salary = parseFloat(formData.gross_salary);
      if (formData.basic_salary) salaryData.known_values.basic_salary = parseFloat(formData.basic_salary);
      if (formData.transport) salaryData.known_values.transport = parseFloat(formData.transport);
      if (formData.housing) salaryData.known_values.housing = parseFloat(formData.housing);
      if (formData.communication) salaryData.known_values.communication = parseFloat(formData.communication);
      if (formData.medical) salaryData.known_values.medical = parseFloat(formData.medical);
      if (formData.overtime) salaryData.known_values.overtime = parseFloat(formData.overtime);
      if (formData.bonus) salaryData.known_values.bonus = parseFloat(formData.bonus);

      const response = await createSalaryFromScenario(
        selectedEmployee!.employee_id,
        companyId,
        salaryData,
        token
      );

      setSuccess(`Salary created successfully for ${selectedEmployee!.full_name}!`);

      // Reset form
      setFormData({
        scenario: 'net_plus_allowances',
        net_salary: '',
        gross_salary: '',
        basic_salary: '',
        transport: '',
        housing: '',
        communication: '',
        medical: '',
        overtime: '',
        bonus: '',
        effective_from: new Date().toISOString().split('T')[0],
      });
      setSelectedEmployee(null);

    } catch (error) {
      console.error('Error creating salary:', error);
      setError(error instanceof Error ? error.message : 'Failed to create salary');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!companies || companies.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-secondary">Please register a company first to manage payroll.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-secondary-dark">Payroll Management</h1>
          <p className="text-secondary mt-1">Create employee salary with allowances</p>
        </div>
      </div>

      {/* Success Message */}
      {success && (
        <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md">
          {success}
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          {error}
        </div>
      )}

      {/* Salary Creation Form */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Employee Selection */}
        <DashboardCard title="Select Employee">
          {isLoadingEmployees ? (
            <div className="text-center py-8">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <p className="mt-2 text-secondary">Loading employees...</p>
            </div>
          ) : (
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {employees.length === 0 ? (
                <p className="text-center text-gray-500 py-4">No employees found</p>
              ) : (
                employees.map((employee) => (
                  <div
                    key={employee.employee_id}
                    onClick={() => handleEmployeeSelect(employee)}
                    className={`p-4 border rounded-lg cursor-pointer transition-all hover:border-primary ${
                      selectedEmployee?.employee_id === employee.employee_id
                        ? 'border-primary bg-primary/5 ring-2 ring-primary/20'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <div className="h-10 w-10 rounded-full bg-primary text-white flex items-center justify-center">
                        <span className="text-sm font-medium">
                          {employee.first_name.charAt(0)}{employee.last_name.charAt(0)}
                        </span>
                      </div>
                      <div className="flex-1">
                        <h3 className="text-sm font-medium text-gray-900">{employee.full_name}</h3>
                        <p className="text-xs text-gray-500">{employee.position || 'No position'}</p>
                        <p className="text-xs text-gray-400">{employee.email || 'No email'}</p>
                      </div>
                      <div className="flex items-center">
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                          employee.status === 'active' ? 'bg-green-100 text-green-800' :
                          employee.status === 'inactive' ? 'bg-red-100 text-red-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}>
                          {employee.status}
                        </span>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          )}
        </DashboardCard>

        {/* Salary Form */}
        <DashboardCard title="Create Salary">
          {!selectedEmployee ? (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
              <p className="text-gray-500">Select an employee to create their salary</p>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Selected Employee Info */}
              <div className="bg-blue-50 p-4 rounded-lg">
                <h3 className="text-sm font-medium text-blue-900 mb-2">Selected Employee</h3>
                <div className="flex items-center space-x-3">
                  <div className="h-8 w-8 rounded-full bg-blue-600 text-white flex items-center justify-center">
                    <span className="text-xs font-medium">
                      {selectedEmployee.first_name.charAt(0)}{selectedEmployee.last_name.charAt(0)}
                    </span>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-blue-900">{selectedEmployee.full_name}</p>
                    <p className="text-xs text-blue-700">{selectedEmployee.position || 'No position'}</p>
                  </div>
                </div>
              </div>

              {/* Scenario Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Salary Scenario *
                </label>
                <select
                  name="scenario"
                  value={formData.scenario}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  required
                >
                  <option value="net_plus_allowances">Net Salary + Allowances</option>
                  <option value="gross_minus_deductions">Gross Salary - Deductions</option>
                  <option value="basic_plus_allowances">Basic Salary + Allowances</option>
                </select>
                <p className="text-xs text-gray-500 mt-1">
                  Choose how you want to calculate the salary
                </p>
              </div>

              {/* Effective Date */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Effective From *
                </label>
                <input
                  type="date"
                  name="effective_from"
                  value={formData.effective_from}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  required
                />
              </div>

              {/* Base Salary Fields */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {formData.scenario === 'net_plus_allowances' && (
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Net Salary (KES) *
                    </label>
                    <input
                      type="number"
                      name="net_salary"
                      value={formData.net_salary}
                      onChange={handleInputChange}
                      placeholder="450000"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      min="0"
                      step="0.01"
                      required
                    />
                  </div>
                )}

                {formData.scenario === 'gross_minus_deductions' && (
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Gross Salary (KES) *
                    </label>
                    <input
                      type="number"
                      name="gross_salary"
                      value={formData.gross_salary}
                      onChange={handleInputChange}
                      placeholder="600000"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      min="0"
                      step="0.01"
                      required
                    />
                  </div>
                )}

                {formData.scenario === 'basic_plus_allowances' && (
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Basic Salary (KES) *
                    </label>
                    <input
                      type="number"
                      name="basic_salary"
                      value={formData.basic_salary}
                      onChange={handleInputChange}
                      placeholder="300000"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      min="0"
                      step="0.01"
                      required
                    />
                  </div>
                )}
              </div>

              {/* Allowances */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-3">Allowances</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Transport Allowance (KES)
                    </label>
                    <input
                      type="number"
                      name="transport"
                      value={formData.transport}
                      onChange={handleInputChange}
                      placeholder="50000"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      min="0"
                      step="0.01"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Housing Allowance (KES)
                    </label>
                    <input
                      type="number"
                      name="housing"
                      value={formData.housing}
                      onChange={handleInputChange}
                      placeholder="100000"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      min="0"
                      step="0.01"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Communication Allowance (KES)
                    </label>
                    <input
                      type="number"
                      name="communication"
                      value={formData.communication}
                      onChange={handleInputChange}
                      placeholder="25000"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      min="0"
                      step="0.01"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Medical Allowance (KES)
                    </label>
                    <input
                      type="number"
                      name="medical"
                      value={formData.medical}
                      onChange={handleInputChange}
                      placeholder="15000"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      min="0"
                      step="0.01"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Overtime (KES)
                    </label>
                    <input
                      type="number"
                      name="overtime"
                      value={formData.overtime}
                      onChange={handleInputChange}
                      placeholder="0"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      min="0"
                      step="0.01"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Bonus (KES)
                    </label>
                    <input
                      type="number"
                      name="bonus"
                      value={formData.bonus}
                      onChange={handleInputChange}
                      placeholder="0"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      min="0"
                      step="0.01"
                    />
                  </div>
                </div>
              </div>

              {/* Submit Button */}
              <div className="flex justify-end space-x-3 pt-6 border-t">
                <button
                  type="button"
                  onClick={() => setSelectedEmployee(null)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  disabled={isSubmitting}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-6 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Creating...
                    </div>
                  ) : (
                    'Create Salary'
                  )}
                </button>
              </div>
            </form>
          )}
        </DashboardCard>
      </div>
    </div>
  );
};

export default PayrollContent;
