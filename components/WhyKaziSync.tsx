import React from 'react';

const whyFeatures = [
  {
    title: 'Real-Time Attendance with IoT device integration',
    icon: '🔗',
  },
  {
    title: 'Secure & Compliant, hosted in the cloud',
    icon: '🔒',
  },
  {
    title: 'Multi-Tenant Architecture – each company has its own isolated database',
    icon: '🏢',
  },
  {
    title: 'Registered in Wyoming, trusted by global teams',
    icon: '🌍',
  },
  {
    title: 'Excellent Support – from onboarding to ongoing optimization',
    icon: '🎯',
  },
];

const WhyKaziSync = () => {
  return (
    <section className="section bg-background">
      <div className="container-custom">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-secondary-dark mb-6">
              Why KaziSync?
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {whyFeatures.map((feature, index) => (
              <div
                key={index}
                className="flex items-start space-x-4 p-6 bg-white rounded-lg border border-gray-200 hover:shadow-md transition-shadow"
              >
                <div className="text-2xl flex-shrink-0">
                  {feature.icon}
                </div>
                <div>
                  <p className="text-secondary-dark font-medium">{feature.title}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhyKaziSync;
