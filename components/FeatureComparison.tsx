import React from 'react';

const features = [
  { name: 'Real-Time Attendance Tracking', starter: true, growth: true, pro: true },
  { name: 'Leave Management', starter: true, growth: true, pro: true },
  { name: 'Payroll Management', starter: true, growth: true, pro: true },
  { name: 'Loan & Advance Management', starter: false, growth: true, pro: true },
  { name: 'Performance Reviews', starter: false, growth: true, pro: true },
  { name: 'Employee Onboarding', starter: false, growth: true, pro: true },
  { name: 'Centralized Document Storage', starter: 'Limited', growth: true, pro: 'Unlimited' },
  { name: 'HR Analytics Dashboard', starter: false, growth: 'Basic', pro: 'Advanced' },
  { name: 'QuickBooks Integration', starter: false, growth: true, pro: true },
  { name: 'Multi-Device IoT Integration', starter: false, growth: 'Up to 2 Devices', pro: 'Unlimited Devices' },
  { name: 'Custom Roles & Permissions', starter: false, growth: true, pro: true },
  { name: 'Dedicated Support / SLA', starter: false, growth: 'Email', pro: 'Priority + Phone' },
  { name: 'Audit Logs & Compliance Reports', starter: false, growth: false, pro: true },
  { name: 'Custom Feature Add-ons', starter: false, growth: false, pro: true },
];

const FeatureComparison = () => {
  const renderFeatureValue = (value: boolean | string) => {
    if (value === true) {
      return (
        <div className="flex justify-center">
          <svg className="h-5 w-5 text-success" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </div>
      );
    } else if (value === false) {
      return (
        <div className="flex justify-center">
          <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </div>
      );
    } else {
      return (
        <div className="text-center text-sm text-secondary-dark font-medium">
          {value}
        </div>
      );
    }
  };

  return (
    <section className="section bg-background">
      <div className="container-custom">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-secondary-dark mb-4">
              Feature Comparison by Plan
            </h2>
            <p className="text-secondary text-lg">
              Each KaziSync plan is tailored to meet the needs of businesses at different stages of growth.
            </p>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-4 text-left text-sm font-semibold text-secondary-dark">
                      Feature
                    </th>
                    <th className="px-6 py-4 text-center text-sm font-semibold text-green-700">
                      🟢 Starter<br />
                      <span className="font-normal text-xs">$49/mo + $2.50/user</span>
                    </th>
                    <th className="px-6 py-4 text-center text-sm font-semibold text-blue-700">
                      🔵 Growth<br />
                      <span className="font-normal text-xs">$99/mo + $2.00/user</span>
                    </th>
                    <th className="px-6 py-4 text-center text-sm font-semibold text-purple-700">
                      🟣 Pro<br />
                      <span className="font-normal text-xs">$199/mo + $1.50/user</span>
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {features.map((feature, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-6 py-4 text-sm text-secondary-dark font-medium">
                        {feature.name}
                      </td>
                      <td className="px-6 py-4">
                        {renderFeatureValue(feature.starter)}
                      </td>
                      <td className="px-6 py-4">
                        {renderFeatureValue(feature.growth)}
                      </td>
                      <td className="px-6 py-4">
                        {renderFeatureValue(feature.pro)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="p-6 bg-green-50 rounded-lg border border-green-200">
              <h3 className="text-lg font-semibold text-green-800 mb-2">🟢 Starter – $49/mo + $2.50/user</h3>
              <p className="text-green-700 text-sm">
                Ideal for small teams just getting started with HR digitization. Includes essentials like attendance, leave, and basic payroll.
              </p>
            </div>
            <div className="p-6 bg-blue-50 rounded-lg border border-blue-200">
              <h3 className="text-lg font-semibold text-blue-800 mb-2">🔵 Growth – $99/mo + $2.00/user</h3>
              <p className="text-blue-700 text-sm">
                Perfect for growing teams that need performance tracking, onboarding, document management, and limited IoT integration.
              </p>
            </div>
            <div className="p-6 bg-purple-50 rounded-lg border border-purple-200">
              <h3 className="text-lg font-semibold text-purple-800 mb-2">🟣 Pro – $199/mo + $1.50/user</h3>
              <p className="text-purple-700 text-sm">
                Enterprise-ready. Includes everything in Growth, plus unlimited IoT devices, audit logging, advanced analytics, and priority support.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeatureComparison;
