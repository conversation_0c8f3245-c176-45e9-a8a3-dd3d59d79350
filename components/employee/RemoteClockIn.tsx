'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import { apiPost } from '@/lib/api';
import { getAccessToken } from '@/lib/auth';
import DashboardCard from '@/components/ui/DashboardCard';

interface LocationData {
  latitude: number;
  longitude: number;
  accuracy: number;
  address?: string;
}

interface ClockInOutResponse {
  code: number;
  msg: string;
  extend?: {
    attendance_id: string;
    status: string;
    clock_in_time?: string;
    clock_out_time?: string;
  };
}

const ClockInOut = () => {
  const { user, companies } = useAuth();
  const [location, setLocation] = useState<LocationData | null>(null);
  const [locationError, setLocationError] = useState<string>('');
  const [isGettingLocation, setIsGettingLocation] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [isClockedIn, setIsClockedIn] = useState(false);
  const [clockInTime, setClockInTime] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  // Update current time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Check if user is already clocked in (from localStorage for demo)
  useEffect(() => {
    const clockedInStatus = localStorage.getItem('clockedIn');
    const clockInTimeStored = localStorage.getItem('clockInTime');
    if (clockedInStatus === 'true') {
      setIsClockedIn(true);
      setClockInTime(clockInTimeStored);
    }
  }, []);

  const getCurrentLocation = () => {
    setIsGettingLocation(true);
    setLocationError('');

    if (!navigator.geolocation) {
      setLocationError('Geolocation is not supported by this browser.');
      setIsGettingLocation(false);
      return;
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const locationData: LocationData = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy,
        };
        setLocation(locationData);
        setIsGettingLocation(false);
      },
      (error) => {
        let errorMessage = 'Unable to retrieve your location.';
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = 'Location access denied. Please enable location permissions.';
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = 'Location information is unavailable.';
            break;
          case error.TIMEOUT:
            errorMessage = 'Location request timed out.';
            break;
        }
        setLocationError(errorMessage);
        setIsGettingLocation(false);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 60000,
      }
    );
  };

  // Get company ID using robust method
  const getCompanyId = () => {
    if (companies && companies.length > 0) {
      return companies[0].company_id;
    }

    const authData = localStorage.getItem('kazisync_auth');
    if (authData) {
      try {
        const parsedData = JSON.parse(authData);
        if (parsedData.company && parsedData.company.company_id) {
          return parsedData.company.company_id;
        }
        if (parsedData.company_id) {
          return parsedData.company_id;
        }
      } catch (error) {
        // Silently handle parsing errors
      }
    }
    return null;
  };

  const handleClockInOut = async () => {
    setLoading(true);
    setMessage(null);

    try {
      // Get location first
      if (!location) {
        await new Promise<void>((resolve, reject) => {
          if (!navigator.geolocation) {
            reject(new Error('Geolocation is not supported'));
            return;
          }

          navigator.geolocation.getCurrentPosition(
            (position) => {
              const locationData: LocationData = {
                latitude: position.coords.latitude,
                longitude: position.coords.longitude,
                accuracy: position.coords.accuracy,
              };
              setLocation(locationData);
              resolve();
            },
            (error) => {
              reject(new Error('Unable to get location'));
            },
            {
              enableHighAccuracy: true,
              timeout: 10000,
              maximumAge: 60000,
            }
          );
        });
      }

      const companyId = getCompanyId();
      const token = getAccessToken();
      const employeeId = (user as any)?.employee_info?.employee_id || (user as any)?.employee_id;

      if (!companyId || !token || !employeeId) {
        throw new Error('Missing required information');
      }

      // For demo purposes, we'll simulate the API call and use localStorage
      // In a real implementation, you would call the actual API
      const currentTimeString = new Date().toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true,
      });

      if (!isClockedIn) {
        // Clock In
        localStorage.setItem('clockedIn', 'true');
        localStorage.setItem('clockInTime', currentTimeString);
        setIsClockedIn(true);
        setClockInTime(currentTimeString);
        setMessage({ type: 'success', text: `Successfully clocked in at ${currentTimeString}` });
      } else {
        // Clock Out
        localStorage.removeItem('clockedIn');
        localStorage.removeItem('clockInTime');
        setIsClockedIn(false);
        setClockInTime(null);
        setMessage({ type: 'success', text: `Successfully clocked out at ${currentTimeString}` });
      }

    } catch (error: any) {
      setMessage({ type: 'error', text: error.message || 'Failed to process clock in/out' });
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true,
    });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <div className="space-y-6">
      {/* Header with Breadcrumb Navigation */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          {/* Breadcrumb Navigation */}
          <div className="flex items-center space-x-2 text-sm text-gray-500 mb-2">
            <Link href="/dashboard/employee" className="hover:text-blue-600">Dashboard</Link>
            <span>/</span>
            <span className="text-gray-900">Clock In/Out</span>
          </div>
          <h1 className="text-2xl font-bold text-gray-900">Clock In/Out</h1>
          <p className="text-sm text-gray-600 mt-1">
            Track your work hours and attendance
          </p>
        </div>
      </div>

      {/* Current Time Display */}
      <DashboardCard>
        <div className="text-center py-8">
          <div className="text-4xl font-bold text-gray-900 mb-2">
            {formatTime(currentTime)}
          </div>
          <div className="text-lg text-gray-600">
            {formatDate(currentTime)}
          </div>
        </div>
      </DashboardCard>

      {/* Status and Messages */}
      {message && (
        <div className={`p-4 rounded-lg ${
          message.type === 'success'
            ? 'bg-green-50 border border-green-200 text-green-800'
            : 'bg-red-50 border border-red-200 text-red-800'
        }`}>
          <div className="flex items-center">
            {message.type === 'success' ? (
              <svg className="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            ) : (
              <svg className="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            )}
            {message.text}
          </div>
        </div>
      )}

      {/* Clock In/Out Interface */}
      <DashboardCard>
        <div className="text-center py-12">
          <div className={`mx-auto flex items-center justify-center h-20 w-20 rounded-full mb-6 ${
            isClockedIn ? 'bg-green-100' : 'bg-blue-100'
          }`}>
            <div className={`w-3 h-3 rounded-full ${
              isClockedIn ? 'bg-green-500 animate-pulse' : 'bg-gray-400'
            }`}></div>
          </div>

          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            {isClockedIn ? 'Currently Working' : 'Ready to Start?'}
          </h2>

          <p className="text-lg text-gray-600 mb-6">
            {isClockedIn
              ? `You clocked in at ${clockInTime}. Click below to clock out.`
              : 'Click the button below to clock in and start your workday.'
            }
          </p>

          {/* Location Status */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6 max-w-md mx-auto">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Location Status:</span>
              <div className="flex items-center">
                {location ? (
                  <>
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                    <span className="text-sm text-green-600">Available</span>
                  </>
                ) : (
                  <>
                    <div className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></div>
                    <span className="text-sm text-yellow-600">Getting location...</span>
                  </>
                )}
              </div>
            </div>
            {locationError && (
              <p className="text-sm text-red-600 mt-2">{locationError}</p>
            )}
          </div>

          {/* Clock In/Out Button */}
          <button
            onClick={handleClockInOut}
            disabled={loading}
            className={`px-12 py-4 rounded-xl font-semibold text-lg text-white transition-all transform hover:scale-105 ${
              isClockedIn
                ? 'bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700'
                : 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700'
            } focus:outline-none focus:ring-4 focus:ring-opacity-50 ${
              isClockedIn ? 'focus:ring-red-300' : 'focus:ring-green-300'
            } disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none`}
          >
            {loading ? (
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-6 w-6 border-2 border-white border-t-transparent mr-3"></div>
                Processing...
              </div>
            ) : (
              <div className="flex items-center">
                <svg className="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  {isClockedIn ? (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                  ) : (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                  )}
                </svg>
                {isClockedIn ? 'Clock Out' : 'Clock In'}
              </div>
            )}
          </button>

          <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/dashboard/employee"
              className="inline-flex items-center px-6 py-3 border border-gray-300 shadow-sm text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            >
              Return to Dashboard
            </Link>
            <Link
              href="/dashboard/employee/attendance"
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            >
              View Attendance History
            </Link>
          </div>
        </div>
      </DashboardCard>
    </div>
  );
};

export default ClockInOut;
