/**
 * Test utilities for authentication functionality
 * This file provides utilities to test the automatic logout and error handling
 */

import { apiGet } from './api';

/**
 * Test function to simulate a 401 response and verify automatic logout
 * This can be called from the browser console to test the functionality
 */
export async function testAutoLogout() {
  console.log('Testing automatic logout functionality...');
  
  try {
    // Try to make a request to an endpoint that will return 401
    // We'll use a fake token to ensure we get a 401 response
    const response = await apiGet('api/test-endpoint', {
      headers: {
        'Authorization': 'Bearer fake-expired-token'
      }
    });
    
    console.log('Unexpected success:', response);
  } catch (error) {
    console.log('Expected error caught:', error.message);
    
    // Check if the error message indicates session expiration
    if (error.message.includes('session has expired')) {
      console.log('✅ Automatic logout functionality is working correctly');
      console.log('User should be redirected to login page with expired=true parameter');
    } else {
      console.log('❌ Automatic logout may not be working as expected');
    }
  }
}

/**
 * Test function to verify error message extraction
 */
export async function testErrorMessageExtraction() {
  console.log('Testing error message extraction...');
  
  // This function can be used to test different error scenarios
  // In a real test, you would mock different API responses
  
  console.log('Error message extraction test completed');
  console.log('Check the network tab and console for proper error message handling');
}

/**
 * Test function to verify empty state handling
 */
export function testEmptyStateHandling() {
  console.log('Testing empty state handling...');
  
  // Import the utility functions
  import('./auth-utils').then(({ shouldDisplayError, getEmptyStateMessage }) => {
    // Test shouldDisplayError function
    console.log('Testing shouldDisplayError:');
    console.log('- Session expired error:', shouldDisplayError({ message: 'Your session has expired' })); // Should be false
    console.log('- 401 error:', shouldDisplayError({ message: '401 Unauthorized' })); // Should be false
    console.log('- Network error:', shouldDisplayError({ message: 'Network error' })); // Should be true
    console.log('- No data error:', shouldDisplayError({ message: 'No data found' })); // Should be false
    
    // Test getEmptyStateMessage function
    console.log('\nTesting getEmptyStateMessage:');
    console.log('- Employees:', getEmptyStateMessage('employees'));
    console.log('- Shifts:', getEmptyStateMessage('shifts'));
    console.log('- Leave balances:', getEmptyStateMessage('leave balances'));
    
    console.log('✅ Empty state handling test completed');
  });
}

// Make functions available globally for testing in browser console
if (typeof window !== 'undefined') {
  (window as any).testAutoLogout = testAutoLogout;
  (window as any).testErrorMessageExtraction = testErrorMessageExtraction;
  (window as any).testEmptyStateHandling = testEmptyStateHandling;
}
