/**
 * Authentication utilities for handling automatic logout
 * This file provides utilities that can be called from anywhere in the app
 */

/**
 * Trigger automatic logout when token expires
 * This function can be called from API utilities or anywhere else
 */
export function triggerAutoLogout(): void {
  // Only run in browser environment
  if (typeof window === 'undefined') return;
  
  try {
    console.log('Triggering automatic logout due to token expiration');
    
    // Clear auth data from localStorage
    localStorage.removeItem('kazisync_auth');
    
    // Redirect to login with expired parameter
    window.location.href = '/login?expired=true';
  } catch (error) {
    console.error('Error during automatic logout:', error);
    // Fallback: clear storage and redirect
    localStorage.removeItem('kazisync_auth');
    window.location.href = '/login';
  }
}

/**
 * Check if the current user is authenticated
 * @returns boolean indicating if user is authenticated
 */
export function isAuthenticated(): boolean {
  if (typeof window === 'undefined') return false;

  try {
    const authData = localStorage.getItem('kazisync_auth');
    if (!authData) return false;

    const parsedData = JSON.parse(authData);
    return !!(parsedData.access_token && parsedData.user && parsedData.user.id);
  } catch (error) {
    console.error('Error checking authentication status:', error);
    return false;
  }
}

/**
 * Check if an error should be displayed to the user or if it's an expected empty state
 * @param error The error object or message
 * @returns boolean indicating if the error should be displayed
 */
export function shouldDisplayError(error: any): boolean {
  if (!error) return false;

  const errorMessage = typeof error === 'string' ? error : error.message || '';

  // Don't display errors for session expiration (handled by auto-logout)
  if (errorMessage.includes('session has expired') ||
      errorMessage.includes('401') ||
      errorMessage.includes('Unauthorized')) {
    return false;
  }

  // Don't display errors for expected empty states
  if (errorMessage.includes('No data found') ||
      errorMessage.includes('No records found') ||
      errorMessage.includes('Empty response')) {
    return false;
  }

  return true;
}

/**
 * Get a user-friendly message for empty data states
 * @param dataType The type of data (e.g., 'employees', 'shifts', 'leave balances')
 * @returns A user-friendly message for empty state
 */
export function getEmptyStateMessage(dataType: string): string {
  const messages: Record<string, string> = {
    'employees': 'No employees found. Add your first employee to get started.',
    'shifts': 'No shifts found. Create your first shift to get started.',
    'departments': 'No departments found. Add your first department to get started.',
    'leave balances': 'No leave balances found. Set up leave balances for your employees.',
    'leave requests': 'No leave requests found.',
    'announcements': 'No announcements found. Create your first announcement.',
    'attendance': 'No attendance data available for the selected date.',
    'companies': 'No companies found. Register your first company to get started.',
    'countries': 'No countries found. Add your first country to get started.',
  };

  return messages[dataType] || `No ${dataType} found.`;
}
