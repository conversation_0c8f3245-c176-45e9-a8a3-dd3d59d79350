// Authentication utilities for KaziSync
import { createApiUrl } from './api';

interface LoginCredentials {
  username: string;
  password: string;
}

interface User {
  employee_id: string;
  employee_info: {
    created_at: string;
    department_id: string;
    email: string;
    employee_id: string;
    first_name: string;
    full_name: string;
    hire_date: string;
    id_number: string | null;
    last_name: string;
    phone_number: string | null;
    position: string | null;
    status: string;
    updated_at: string;
  };
  id: string;
  name: string;
  role: string;
  username: string;
}

export function isAuthenticated(): boolean {
  // Simple check if we have a user
  return !!getCurrentUser();
}

interface Company {
  company_id: string;
  company_name: string;
  database_name?: string;
}

interface UserResponse {
  success: boolean;
  user: {
    companies: Company[];
    company_names: string[];
    created_at: string;
    email: string;
    first_name: string;
    full_name: string;
    last_name: string;
    phone_number: string;
    role: string;
    user_id: string;
    username: string;
  };
}

interface AuthResponse {
  access_token: string;
  authenticated: boolean;
  companies?: Company[];  // For HR users
  company?: Company;      // For employee users
  refresh_token: string;
  user: User;
}

/**
 * Authenticate a user with the API
 * @param credentials User credentials (username and password)
 * @returns Authentication response with tokens and user info
 */
export async function login(credentials: LoginCredentials): Promise<AuthResponse> {
  // Add timestamp for tracking login attempts
  const startTime = new Date().toISOString();
  console.log(`[${startTime}] Login attempt started for user: ${credentials.username}`);

  try {
    console.log('Login request with credentials:', credentials);

    // Make the API call using our utility function
    const url = createApiUrl('login');
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(credentials),
    });

    console.log('Login response status:', response.status);

    // Clone the response to read it twice (once for logging, once for processing)
    const responseClone = response.clone();
    const responseText = await responseClone.text();
    console.log('Login response body:', responseText);

    if (!response.ok) {
      // Try to extract error message from response
      let errorMessage = 'Authentication failed';
      try {
        if (responseText) {
          const errorData = JSON.parse(responseText);
          if (errorData.message) {
            errorMessage = errorData.message;
          }
        }
      } catch (parseError) {
        // If we can't parse the error response, use fallback based on status
        switch (response.status) {
          case 401:
            errorMessage = 'Invalid credentials. Please check your username and password.';
            break;
          case 403:
            errorMessage = 'Access denied. Your account may be disabled.';
            break;
          case 429:
            errorMessage = 'Too many login attempts. Please try again later.';
            break;
          case 500:
            errorMessage = 'Server error. Please try again later.';
            break;
          default:
            errorMessage = `Authentication failed: ${response.status} ${response.statusText}`;
        }
      }
      throw new Error(errorMessage);
    }

    // Parse the response text instead of calling response.json() again
    let data: AuthResponse = {
      access_token: '',
      authenticated: false,
      refresh_token: '',
      user: {
        employee_id: '',
        employee_info: {
          created_at: '',
          department_id: '',
          email: '',
          employee_id: '',
          first_name: '',
          full_name: '',
          hire_date: '',
          id_number: null,
          last_name: '',
          phone_number: null,
          position: null,
          status: '',
          updated_at: ''
        },
        id: '',
        name: '',
        role: '',
        username: ''
      }
    };

    try {
      data = responseText ? JSON.parse(responseText) : data;
      console.log('Parsed response data:', data);
    } catch (parseError) {
      console.error('Error parsing JSON response:', parseError);
      console.log('Raw response text:', responseText);
      throw new Error('Invalid response format from server');
    }

    // Store auth data in localStorage
    if (data.authenticated) {
      // Make sure we have a valid user object before storing
      if (!data.user || !data.user.id || !data.user.role) {
        throw new Error('Invalid user data received from server');
      }

      // Store the data in localStorage
      localStorage.setItem('kazisync_auth', JSON.stringify(data));
    }

    // Log successful login
    console.log(`[${new Date().toISOString()}] Login ${data.authenticated ? 'successful' : 'failed'} for user: ${credentials.username}`);
    if (data.authenticated) {
      console.log(`User role: ${data.user.role}`);
    }

    return data;
  } catch (error) {
    // Log detailed error information
    console.error(`[${new Date().toISOString()}] Login error for user ${credentials.username}:`, error);

    // Handle error object properly with type checking
    if (error instanceof Error) {
      console.error('Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack
      });
    } else {
      console.error('Unknown error type:', error);
    }

    throw error;
  } finally {
    console.log(`[${new Date().toISOString()}] Login attempt completed for user: ${credentials.username}`);
  }
}

/**
 * Get the current authenticated user from localStorage
 * @returns User object or null if not authenticated
 */
export function getCurrentUser(): User | null {
  if (typeof window === 'undefined') {
    return null; // Return null during SSR
  }

  // Try to get auth data from localStorage
  const authData = localStorage.getItem('kazisync_auth');
  if (!authData) return null;

  try {
    const parsedData = JSON.parse(authData);

    if (!parsedData.user) {
      return null;
    }

    // Validate that the user object has the required fields
    const user = parsedData.user;
    if (!user.id || !user.role) {
      return null;
    }

    return user;
  } catch (error) {
    console.error('Error parsing auth data:', error);
    return null;
  }
}

/**
 * Get the companies for the current authenticated user
 * @returns Array of Company objects or empty array if not authenticated
 */
export function getCurrentUserCompanies(): Company[] {
  if (typeof window === 'undefined') {
    return []; // Return empty array during SSR
  }

  // Try to get auth data from localStorage
  const authData = localStorage.getItem('kazisync_auth');
  if (!authData) return [];

  try {
    const parsedData = JSON.parse(authData);

    // Check if companies array exists (HR users)
    if (parsedData.companies && Array.isArray(parsedData.companies)) {
      return parsedData.companies;
    }

    // Check if single company object exists (employee users)
    if (parsedData.company && parsedData.company.company_id) {
      return [parsedData.company];
    }

    return [];
  } catch (error) {
    console.error('Error parsing auth data for companies:', error);
    return [];
  }
}

/**
 * Get the stored access token
 * @returns Access token string or null if not authenticated
 */
export function getAccessToken(): string | null {
  if (typeof window === 'undefined') {
    return null; // Return null during SSR
  }

  const authData = localStorage.getItem('kazisync_auth');
  if (!authData) return null;

  try {
    const parsedData = JSON.parse(authData);
    return parsedData.access_token || null;
  } catch (error) {
    console.error('Error parsing auth data:', error);
    return null;
  }
}

/**
 * Log out the current user
 */
export function logout(): void {
  // Clear auth data from localStorage
  localStorage.removeItem('kazisync_auth');

  console.log('User logged out, auth data cleared');
  // Redirect to login page can be handled by the component
}

/**
 * Get the dashboard path based on user role
 * @param role User role
 * @returns Dashboard path
 */
// Create a mapping object for faster lookups
const ROLE_DASHBOARD_MAP: Record<string, string> = {
  'super-admin': '/dashboard/super-admin',
  'company-admin': '/dashboard/admin',
  'admin': '/dashboard/admin',
  'hr-manager': '/dashboard/hr',
  'hr': '/dashboard/hr',
  'manager': '/dashboard/manager',
  'employee': '/dashboard/employee',
  'user': '/dashboard/employee' // Map 'user' role to employee dashboard
};

export function getDashboardPathByRole(role: string): string {
  if (!role) {
    return '/dashboard';
  }

  // Convert to lowercase and normalize spaces to hyphens for consistent lookup
  const normalizedRole = role.toLowerCase().replace(/\s+/g, '-');

  // Use the map for direct lookup instead of switch statement
  const dashboardPath = ROLE_DASHBOARD_MAP[normalizedRole];

  if (!dashboardPath) {
    return '/dashboard';
  }

  return dashboardPath;
}

/**
 * Fetch user data from the API
 * @param userId The user ID
 * @returns User data including company information
 */
export async function fetchUserData(userId: string): Promise<UserResponse> {
  // Import apiGet dynamically to avoid circular dependencies
  const { apiGet } = await import('./api');
  const token = getAccessToken();

  if (!token) {
    throw new Error('Authentication required');
  }

  try {
    const response = await apiGet<UserResponse>(`get_user/${userId}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    // If successful, update the local storage with the latest company data
    if (response.success && response.user) {
      // Get the current auth data
      const authData = localStorage.getItem('kazisync_auth');
      if (authData) {
        try {
          const parsedData = JSON.parse(authData);

          // Update the companies array
          parsedData.companies = response.user.companies;

          // Save the updated data back to localStorage
          localStorage.setItem('kazisync_auth', JSON.stringify(parsedData));
        } catch (error) {
          console.error('Error updating auth data with user info:', error);
        }
      }
    }

    return response;
  } catch (error) {
    console.error('Error fetching user data:', error);
    throw error;
  }
}
