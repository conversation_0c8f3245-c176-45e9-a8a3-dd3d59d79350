// lib/announcements.ts

import { apiGet, apiPost, apiPut, apiDelete } from './api';
import { getAccessToken } from './auth';
import {
  Announcement,
  CreateAnnouncementRequest,
  UpdateAnnouncementRequest,
  AnnouncementResponse,
  AnnouncementListResponse,
  DeleteAnnouncementResponse,
  AnnouncementAnalyticsResponse,
  AnnouncementReadResponse
} from '@/types/announcement';

/**
 * Create a new announcement
 */
export async function createAnnouncement(data: CreateAnnouncementRequest): Promise<Announcement> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiPost<AnnouncementResponse>('api/announcements', data, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });

  return response.extend.announcement;
}

/**
 * Get all announcements for a company
 */
export async function getAnnouncements(
  companyId: string,
  options?: {
    page?: number;
    limit?: number;
    is_published?: boolean;
    priority?: string;
    announcement_type?: string;
  }
): Promise<{ announcements: Announcement[]; pagination?: any }> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  let url = `api/announcements?company_id=${companyId}`;

  if (options) {
    if (options.page) url += `&page=${options.page}`;
    if (options.limit) url += `&limit=${options.limit}`;
    if (options.is_published !== undefined) url += `&is_published=${options.is_published}`;
    if (options.priority) url += `&priority=${options.priority}`;
    if (options.announcement_type) url += `&announcement_type=${options.announcement_type}`;
  }

  const response = await apiGet<AnnouncementListResponse>(url, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });

  return {
    announcements: response.announcements,
    pagination: response.pagination
  };
}

/**
 * Get announcements for employee dashboard (published only)
 */
export async function getEmployeeAnnouncements(
  companyId: string
): Promise<Announcement[]> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const url = `api/announcements?company_id=${companyId}&is_published=true`;

  const response = await apiGet<AnnouncementListResponse>(url, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });

  return response.announcements;
}

/**
 * Get a single announcement by ID
 */
export async function getAnnouncement(
  announcementId: string,
  companyId: string
): Promise<Announcement> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiGet<AnnouncementResponse>(
    `api/announcements/${announcementId}?company_id=${companyId}`,
    {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    }
  );

  return response.extend.announcement;
}

/**
 * Update an announcement
 */
export async function updateAnnouncement(
  announcementId: string,
  data: UpdateAnnouncementRequest
): Promise<Announcement> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiPut<AnnouncementResponse>(
    `api/announcements/${announcementId}`,
    data,
    {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    }
  );

  return response.extend.announcement;
}

/**
 * Delete an announcement
 */
export async function deleteAnnouncement(
  announcementId: string,
  companyId: string
): Promise<string> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiDelete<DeleteAnnouncementResponse>(
    `api/announcements/${announcementId}?company_id=${companyId}`,
    {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    }
  );

  return response.extend.message;
}

/**
 * Publish an announcement
 */
export async function publishAnnouncement(
  announcementId: string,
  companyId: string
): Promise<void> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  await apiPost(
    `api/announcements/${announcementId}/publish?company_id=${companyId}`,
    {},
    {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    }
  );
}

/**
 * Unpublish an announcement
 */
export async function unpublishAnnouncement(
  announcementId: string,
  companyId: string
): Promise<void> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  await apiPost(
    `api/announcements/${announcementId}/unpublish?company_id=${companyId}`,
    {},
    {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    }
  );
}

/**
 * Archive an announcement
 */
export async function archiveAnnouncement(
  announcementId: string,
  companyId: string
): Promise<void> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  await apiPost(
    `api/announcements/${announcementId}/archive?company_id=${companyId}`,
    {},
    {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    }
  );
}

/**
 * Get announcement analytics
 */
export async function getAnnouncementAnalytics(
  announcementId: string,
  companyId: string
): Promise<AnnouncementAnalyticsResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiGet<AnnouncementAnalyticsResponse>(
    `api/announcements/${announcementId}/analytics?company_id=${companyId}`,
    {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    }
  );

  return response;
}

/**
 * Get announcement read records
 */
export async function getAnnouncementReads(
  announcementId: string,
  companyId: string,
  options?: {
    page?: number;
    limit?: number;
  }
): Promise<AnnouncementReadResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  let url = `api/announcements/${announcementId}/reads?company_id=${companyId}`;

  if (options) {
    if (options.page) url += `&page=${options.page}`;
    if (options.limit) url += `&limit=${options.limit}`;
  }

  const response = await apiGet<AnnouncementReadResponse>(url, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });

  return response;
}
