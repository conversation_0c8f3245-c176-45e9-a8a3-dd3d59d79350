/**
 * Payroll API utilities for KaziSync
 * This file contains API functions for payroll management
 */

import { apiPost } from './api';

export interface SalaryScenarioData {
  scenario: 'net_plus_allowances' | 'gross_minus_deductions' | 'basic_plus_allowances';
  known_values: {
    net_salary?: number;
    gross_salary?: number;
    basic_salary?: number;
    transport?: number;
    housing?: number;
    communication?: number;
    medical?: number;
    overtime?: number;
    bonus?: number;
    tax?: number;
    nhif?: number;
    nssf?: number;
    pension?: number;
  };
  effective_from: string;
}

export interface SalaryResponse {
  scenario: string;
  known_values: {
    net_salary?: number;
    gross_salary?: number;
    basic_salary?: number;
    transport?: number;
    housing?: number;
    communication?: number;
    medical?: number;
    overtime?: number;
    bonus?: number;
  };
  effective_from: string;
}

// Simple interface for storing salary info locally
export interface EmployeeSalaryInfo {
  employee_id: string;
  net_salary?: number;
  gross_salary?: number;
  basic_salary?: number;
  transport?: number;
  housing?: number;
  communication?: number;
  effective_from?: string;
  scenario?: string;
}

/**
 * Create salary from scenario for an employee
 * @param employeeId The employee ID
 * @param companyId The company ID
 * @param salaryData The salary scenario data
 * @param token The authentication token
 * @returns Promise with the salary creation response
 */
export async function createSalaryFromScenario(
  employeeId: string,
  companyId: string,
  salaryData: SalaryScenarioData,
  token: string
): Promise<SalaryResponse> {
  try {
    const response = await apiPost<SalaryResponse>(
      `api/employees/${employeeId}/salary/create-from-scenario?company_id=${companyId}`,
      salaryData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );

    return response;
  } catch (error) {
    console.error('Error creating salary from scenario:', error);
    throw error;
  }
}






