// lib/payroll.ts

import { apiGet, apiPost, apiPatch, apiDelete } from './api';
import { getAccessToken } from './auth';
import {
  PayrollPolicyType,
  CreatePayrollPolicyTypeRequest,
  UpdatePayrollPolicyTypeRequest,
  PayrollPolicyTypesResponse,
  PayrollPolicyTypeResponse,
  PolicyTypeCountriesResponse,
  Country,
  CreateCountryRequest,
  UpdateCountryRequest,
  CountriesResponse,
  CountryResponse
} from '@/types/payroll';

/**
 * Create a new payroll policy type
 */
export async function createPayrollPolicyType(data: CreatePayrollPolicyTypeRequest): Promise<PayrollPolicyType> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiPost<PayrollPolicyTypeResponse>('api/payroll/policy-types', data, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });

  return response.policy_type;
}

/**
 * Get all payroll policy types
 */
export async function getPayrollPolicyTypes(): Promise<{ policyTypes: PayrollPolicyType[]; totalCount: number }> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiGet<PayrollPolicyTypesResponse>('api/payroll/policy-types', {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });

  return {
    policyTypes: response.policy_types,
    totalCount: response.total_count
  };
}

/**
 * Get a single payroll policy type by ID
 */
export async function getPayrollPolicyType(policyTypeId: string): Promise<PayrollPolicyType> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiGet<PayrollPolicyTypeResponse>(
    `api/payroll/policy-types/${policyTypeId}`,
    {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    }
  );

  return response.policy_type;
}

/**
 * Update a payroll policy type
 */
export async function updatePayrollPolicyType(
  policyTypeId: string,
  data: UpdatePayrollPolicyTypeRequest
): Promise<PayrollPolicyType> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiPatch<PayrollPolicyTypeResponse>(
    `api/payroll/policy-types/${policyTypeId}`,
    data,
    {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    }
  );

  return response.policy_type;
}

/**
 * Delete a payroll policy type
 */
export async function deletePayrollPolicyType(policyTypeId: string): Promise<void> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  await apiDelete(`api/payroll/policy-types/${policyTypeId}`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
}

/**
 * Get countries using a specific policy type
 */
export async function getPolicyTypeCountries(policyTypeId: string): Promise<PolicyTypeCountriesResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiGet<PolicyTypeCountriesResponse>(
    `api/payroll/policy-types/${policyTypeId}/countries`,
    {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    }
  );

  return response;
}

// Countries API Functions

/**
 * Create a new country
 */
export async function createCountry(data: CreateCountryRequest): Promise<Country> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiPost<CountryResponse>('api/countries', data, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });

  return response.country;
}

/**
 * Get all countries
 */
export async function getCountries(): Promise<{ countries: Country[]; totalCount: number }> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiGet<CountriesResponse>('api/countries', {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });

  return {
    countries: response.countries,
    totalCount: response.total_count
  };
}

/**
 * Get a single country by ID
 */
export async function getCountry(countryId: string): Promise<Country> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiGet<CountryResponse>(`api/countries/${countryId}`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });

  return response.country;
}

/**
 * Update a country
 */
export async function updateCountry(
  countryId: string,
  data: UpdateCountryRequest
): Promise<Country> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiPatch<CountryResponse>(`api/countries/${countryId}`, data, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });

  return response.country;
}

/**
 * Delete a country
 */
export async function deleteCountry(countryId: string): Promise<void> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  await apiDelete(`api/countries/${countryId}`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
}
