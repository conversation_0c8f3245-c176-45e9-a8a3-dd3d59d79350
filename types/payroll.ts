// types/payroll.ts

export interface PayrollPolicyType {
  policy_type_id: string;
  name: string;
  code: string;
  calculation_method: CalculationMethod;
  description: string;
  is_mandatory: boolean;
  is_active: boolean;
  applies_to: AppliesTo;
  created_at: string;
  updated_at: string;
  usage_stats?: {
    countries_using: number;
    is_in_use: boolean;
    total_policies: number;
  };
  detailed_usage?: {
    active_policies: number;
    countries_using: string[];
    policies: any[];
    total_policies: number;
  };
}

export type CalculationMethod = 
  | 'PROGRESSIVE_TAX'
  | 'FLAT_RATE'
  | 'PERCENTAGE'
  | 'FIXED_AMOUNT'
  | 'TIERED'
  | 'CUSTOM';

export type AppliesTo = 
  | 'ALL_EMPLOYEES'
  | 'SPECIFIC_ROLES'
  | 'SPECIFIC_DEPARTMENTS'
  | 'CUSTOM';

export interface CreatePayrollPolicyTypeRequest {
  name: string;
  code: string;
  calculation_method: CalculationMethod;
  description: string;
  is_mandatory: boolean;
  applies_to?: AppliesTo;
}

export interface UpdatePayrollPolicyTypeRequest {
  name?: string;
  description?: string;
  is_active?: boolean;
  calculation_method?: CalculationMethod;
  is_mandatory?: boolean;
  applies_to?: AppliesTo;
}

export interface PayrollPolicyTypesResponse {
  policy_types: PayrollPolicyType[];
  success: boolean;
  total_count: number;
}

export interface PayrollPolicyTypeResponse {
  policy_type: PayrollPolicyType;
  success: boolean;
}

export interface PolicyTypeCountriesResponse {
  countries: any[];
  policy_type: {
    code: string;
    name: string;
    policy_type_id: string;
  };
  success: boolean;
  total_countries: number;
}

// Country types
export interface Country {
  country_id: string;
  name: string;
  code: string;
  currency: string;
  time_zone: string;
  date_format?: string;
  created_at: string;
  updated_at: string;
  payroll_status?: {
    deduction_types_count: number;
    employee_types_count: number;
    is_ready: boolean;
    policies_count: number;
    setup_completion: number;
  };
}

export interface CreateCountryRequest {
  name: string;
  code: string;
  currency: string;
  time_zone: string;
  date_format?: string;
}

export interface UpdateCountryRequest {
  name?: string;
  code?: string;
  currency?: string;
  time_zone?: string;
  date_format?: string;
}

export interface CountriesResponse {
  countries: Country[];
  success: boolean;
  total_count: number;
}

export interface CountryResponse {
  country: Country;
  message: string;
  success: boolean;
}

// Helper functions
export const getCalculationMethodLabel = (method: CalculationMethod): string => {
  switch (method) {
    case 'PROGRESSIVE_TAX':
      return 'Progressive Tax';
    case 'FLAT_RATE':
      return 'Flat Rate';
    case 'PERCENTAGE':
      return 'Percentage';
    case 'FIXED_AMOUNT':
      return 'Fixed Amount';
    case 'TIERED':
      return 'Tiered';
    case 'CUSTOM':
      return 'Custom';
    default:
      return method;
  }
};

export const getAppliesToLabel = (appliesTo: AppliesTo): string => {
  switch (appliesTo) {
    case 'ALL_EMPLOYEES':
      return 'All Employees';
    case 'SPECIFIC_ROLES':
      return 'Specific Roles';
    case 'SPECIFIC_DEPARTMENTS':
      return 'Specific Departments';
    case 'CUSTOM':
      return 'Custom';
    default:
      return appliesTo;
  }
};

export const getPayrollStatusColor = (setupCompletion: number): string => {
  if (setupCompletion >= 80) return 'text-green-600 bg-green-100';
  if (setupCompletion >= 50) return 'text-yellow-600 bg-yellow-100';
  return 'text-red-600 bg-red-100';
};

export const getPayrollStatusLabel = (setupCompletion: number): string => {
  if (setupCompletion >= 80) return 'Ready';
  if (setupCompletion >= 50) return 'In Progress';
  return 'Not Ready';
};
