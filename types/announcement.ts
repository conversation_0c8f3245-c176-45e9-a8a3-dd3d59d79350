// types/announcement.ts

export interface Announcement {
  announcement_id: string;
  title: string;
  content: string;
  summary?: string;
  announcement_type: AnnouncementType;
  priority: Priority;
  category?: string;
  tags: string[];
  target_audience: TargetAudience;
  publish_date?: string;
  expiry_date?: string;
  is_pinned: boolean;
  allows_comments: boolean;
  requires_acknowledgment: boolean;
  attachment_urls: string[];
  department_ids: string[];
  employee_ids: string[];
  role_targets: string[];
  is_active: boolean;
  is_published: boolean;
  scheduled_publish: boolean;
  view_count: number;
  read_count: number;
  read_percentage: number;
  acknowledgment_count: number;
  total_target_employees: number;
  created_at: string;
  updated_at: string;
  created_by: string;
  updated_by?: string;
}

export type AnnouncementType = 
  | 'GENERAL'
  | 'POLICY'
  | 'EVENT'
  | 'URGENT'
  | 'MAINTENANCE'
  | 'CELEBRATION'
  | 'TRAINING'
  | 'MEETING';

export type Priority = 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';

export type TargetAudience =
  | 'ALL'
  | 'DEPARTMENT_SPECIFIC'
  | 'ROLE_SPECIFIC'
  | 'EMPLOYEE_SPECIFIC'
  | 'CUSTOM';

export interface CreateAnnouncementRequest {
  title: string;
  content: string;
  company_id: string;
  summary?: string;
  announcement_type: AnnouncementType;
  priority: Priority;
  category?: string;
  tags?: string[];
  target_audience: TargetAudience;
  publish_date?: string;
  expiry_date?: string;
  is_pinned?: boolean;
  allows_comments?: boolean;
  requires_acknowledgment?: boolean;
  department_ids?: string[];
  employee_ids?: string[];
  role_targets?: string[];
}

export interface UpdateAnnouncementRequest {
  company_id: string;
  title?: string;
  content?: string;
  summary?: string;
  announcement_type?: AnnouncementType;
  priority?: Priority;
  category?: string;
  tags?: string[];
  target_audience?: TargetAudience;
  publish_date?: string;
  expiry_date?: string;
  is_pinned?: boolean;
  allows_comments?: boolean;
  requires_acknowledgment?: boolean;
  department_ids?: string[];
  employee_ids?: string[];
  role_targets?: string[];
}

export interface AnnouncementResponse {
  code?: number;
  extend: {
    announcement: Announcement;
  };
  msg: string;
}

export interface AnnouncementListResponse {
  announcements: Announcement[];
  message: string;
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface DeleteAnnouncementResponse {
  code: number;
  extend: {
    message: string;
  };
  msg: string;
}

// Analytics interfaces
export interface AnnouncementAnalytics {
  announcement: Announcement;
  daily_reads: Array<{
    date: string;
    reads: number;
  }>;
  total_reads: number;
  unique_readers: number;
}

export interface AnnouncementAnalyticsResponse {
  analytics: AnnouncementAnalytics;
  message: string;
}

export interface AnnouncementReadRecord {
  read_id: string;
  announcement_id: string;
  employee_id: string;
  read_at: string;
  acknowledged_at?: string;
  time_spent?: number;
  device_type?: string;
  employee_name?: string;
  department?: string;
}

export interface AnnouncementReadResponse {
  reads: AnnouncementReadRecord[];
  message: string;
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
  statistics: {
    total_reads: number;
    unique_readers: number;
    acknowledged_reads: number;
    acknowledgment_rate: number;
    average_time_spent?: number;
    device_breakdown: Array<{
      device_type: string;
      count: number;
    }>;
  };
}

// Helper functions for announcement types
export const getAnnouncementTypeColor = (type: AnnouncementType): string => {
  switch (type) {
    case 'URGENT':
      return 'bg-red-100 text-red-800';
    case 'POLICY':
      return 'bg-blue-100 text-blue-800';
    case 'EVENT':
      return 'bg-green-100 text-green-800';
    case 'MAINTENANCE':
      return 'bg-yellow-100 text-yellow-800';
    case 'CELEBRATION':
      return 'bg-purple-100 text-purple-800';
    case 'TRAINING':
      return 'bg-indigo-100 text-indigo-800';
    case 'MEETING':
      return 'bg-orange-100 text-orange-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export const getPriorityColor = (priority: Priority): string => {
  switch (priority) {
    case 'URGENT':
      return 'bg-red-500 text-white';
    case 'HIGH':
      return 'bg-orange-500 text-white';
    case 'MEDIUM':
      return 'bg-yellow-500 text-white';
    case 'LOW':
      return 'bg-green-500 text-white';
    default:
      return 'bg-gray-500 text-white';
  }
};

export const getPriorityIcon = (priority: Priority): string => {
  switch (priority) {
    case 'URGENT':
      return '🚨';
    case 'HIGH':
      return '⚠️';
    case 'MEDIUM':
      return '📢';
    case 'LOW':
      return 'ℹ️';
    default:
      return '📝';
  }
};
