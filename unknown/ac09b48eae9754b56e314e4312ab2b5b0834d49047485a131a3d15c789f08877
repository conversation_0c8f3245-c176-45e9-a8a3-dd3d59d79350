'use client';

import React from 'react';
import { Announcement, getAnnouncementTypeColor, getPriorityColor, getPriorityIcon } from '@/types/announcement';

interface AnnouncementCardProps {
  announcement: Announcement;
  isEmployee?: boolean;
  onEdit?: (announcement: Announcement) => void;
  onDelete?: (announcement: Announcement) => void;
  onPublish?: (announcement: Announcement) => void;
  onUnpublish?: (announcement: Announcement) => void;
  onArchive?: (announcement: Announcement) => void;
  onAnalytics?: (announcement: Announcement) => void;
  onClick?: (announcement: Announcement) => void;
}

const AnnouncementCard: React.FC<AnnouncementCardProps> = ({
  announcement,
  isEmployee = false,
  onEdit,
  onDelete,
  onPublish,
  onUnpublish,
  onArchive,
  onAnalytics,
  onClick
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const isExpired = announcement.expiry_date && new Date(announcement.expiry_date) < new Date();
  const isScheduled = announcement.publish_date && new Date(announcement.publish_date) > new Date();

  return (
    <div 
      className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow ${
        onClick ? 'cursor-pointer' : ''
      } ${isExpired ? 'opacity-75' : ''}`}
      onClick={() => onClick && onClick(announcement)}
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            {announcement.is_pinned && (
              <svg className="h-4 w-4 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                <path d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v8H4V6z"/>
              </svg>
            )}
            <h3 className="text-lg font-semibold text-gray-900 line-clamp-2">
              {announcement.title}
            </h3>
          </div>
          
          {announcement.summary && (
            <p className="text-sm text-gray-600 mb-3 line-clamp-2">
              {announcement.summary}
            </p>
          )}
        </div>

        {/* Priority Badge */}
        <div className="flex items-center gap-2 ml-4">
          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(announcement.priority)}`}>
            <span className="mr-1">{getPriorityIcon(announcement.priority)}</span>
            {announcement.priority}
          </span>
        </div>
      </div>

      {/* Content Preview */}
      <div className="mb-4">
        <p className="text-sm text-gray-700 line-clamp-3">
          {announcement.content}
        </p>
      </div>

      {/* Tags and Type */}
      <div className="flex flex-wrap gap-2 mb-4">
        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getAnnouncementTypeColor(announcement.announcement_type)}`}>
          {announcement.announcement_type}
        </span>
        
        {announcement.category && (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            {announcement.category}
          </span>
        )}

        {announcement.tags.slice(0, 3).map((tag, index) => (
          <span key={index} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-50 text-blue-700">
            #{tag}
          </span>
        ))}
        
        {announcement.tags.length > 3 && (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-50 text-gray-600">
            +{announcement.tags.length - 3} more
          </span>
        )}
      </div>

      {/* Status Indicators */}
      <div className="flex items-center gap-2 mb-4">
        {!announcement.is_published && (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            Draft
          </span>
        )}
        
        {isScheduled && (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            Scheduled
          </span>
        )}
        
        {isExpired && (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
            Expired
          </span>
        )}

        {announcement.requires_acknowledgment && (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
            Requires Acknowledgment
          </span>
        )}
      </div>

      {/* Footer */}
      <div className="flex items-center justify-between pt-4 border-t border-gray-100">
        <div className="text-xs text-gray-500">
          <div>Created: {formatDate(announcement.created_at)}</div>
          {announcement.publish_date && (
            <div>Publish: {formatDate(announcement.publish_date)}</div>
          )}
          {announcement.expiry_date && (
            <div>Expires: {formatDate(announcement.expiry_date)}</div>
          )}
          {!isEmployee && announcement.is_published && (
            <div className="mt-1 flex items-center space-x-3">
              <span>👁️ {announcement.read_count} reads</span>
              <span>✅ {announcement.acknowledgment_count} acks</span>
              {announcement.total_target_employees > 0 && (
                <span>📊 {Math.round((announcement.read_count / announcement.total_target_employees) * 100)}% reach</span>
              )}
            </div>
          )}
        </div>

        {/* Action Buttons for HR */}
        {!isEmployee && (
          <div className="flex items-center gap-2 flex-wrap">
            {onAnalytics && announcement.is_published && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onAnalytics(announcement);
                }}
                className="text-purple-600 hover:text-purple-800 text-sm font-medium"
              >
                Analytics
              </button>
            )}

            {onEdit && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onEdit(announcement);
                }}
                className="text-blue-600 hover:text-blue-800 text-sm font-medium"
              >
                Edit
              </button>
            )}

            {onPublish && !announcement.is_published && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onPublish(announcement);
                }}
                className="text-green-600 hover:text-green-800 text-sm font-medium"
              >
                Publish
              </button>
            )}

            {onUnpublish && announcement.is_published && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onUnpublish(announcement);
                }}
                className="text-yellow-600 hover:text-yellow-800 text-sm font-medium"
              >
                Unpublish
              </button>
            )}

            {onArchive && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onArchive(announcement);
                }}
                className="text-orange-600 hover:text-orange-800 text-sm font-medium"
              >
                Archive
              </button>
            )}

            {onDelete && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onDelete(announcement);
                }}
                className="text-red-600 hover:text-red-800 text-sm font-medium"
              >
                Delete
              </button>
            )}
          </div>
        )}

        {/* View Count for Employee */}
        {isEmployee && (
          <div className="text-xs text-gray-500">
            {announcement.view_count} views
          </div>
        )}
      </div>
    </div>
  );
};

export default AnnouncementCard;
